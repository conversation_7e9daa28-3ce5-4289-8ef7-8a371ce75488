import { useMemo } from 'react';
import { isBroadcastOrChannel } from '../../../../../model/MessageType';
import { Active } from '../../../../../model/Statuses';
import useIsMessageMine from './useIsMessageMine';
import IMessage from '../../../../abstract/Messages/IMessage';

export default function useIsMessageReadonly(
  selectedItem: IMessage | undefined,
): boolean {
  const isMessageMine = useIsMessageMine();

  return useMemo<boolean>(
    () =>
      (!!selectedItem?.id && selectedItem?.status !== Active.value) ||
      (isBroadcastOrChannel(selectedItem) && !isMessageMine(selectedItem)),
    [selectedItem, isMessageMine],
  );
}
