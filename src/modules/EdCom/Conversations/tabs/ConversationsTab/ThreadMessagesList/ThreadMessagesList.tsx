import React, { useCallback, useMemo, forwardRef } from 'react';
import { DocumentNode } from 'graphql';
import { map } from 'lodash';

import FetchMoreScroll from '../../../../../../common/utils/infinityScroll/FetchMoreScroll';
import ThreadMessage from '../ThreadMessage/ThreadMessage';

import IThreadMessageItem from '../abstract/IThreadMessageItem';
import FetchMoreButton from './FetchMoreButton';
import useThreadMessagesList from '../hook/useThreadMessagesList';
import ThreadMessageForm from '../ThreadMessageForm';
import style from './ThreadMessagesList.scss';
import ThreadMessageReadedProvider from '../context/ThreadMessageReadedProvider';
import useAnchor from '../hook/useAnchor';
import useSocketHandler from '../../../../../../common/data/socket/useSocketHandler';
import useCurrentUser from '../../../../../../common/data/hooks/useCurrentUser';
import ConversationMessageNotificationType from '../../../model/ConversationMessageNotificationType';
import { TextEditorRef } from '../../../../../../common/components/controls/base/TextEditor/TextEditor';

interface IThreadMessagesList {
  messageId: number;
  conversationId: number;
  gqlConnectionQuery: DocumentNode;
  gqlConnectionVariables?: {};
  isEditMode?: boolean;
  setIsEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  editingMessageId?: number | null;
  setEditingMessageId?: React.Dispatch<React.SetStateAction<number | null>>;
  editingMessage?: IThreadMessageItem;
  showTextEditor?: boolean;
  showReplies?: boolean;
}

const ThreadMessagesList = forwardRef<TextEditorRef, IThreadMessagesList>(
  (
    {
      gqlConnectionQuery,
      gqlConnectionVariables,
      messageId,
      conversationId,
      isEditMode,
      setIsEditMode,
      editingMessageId,
      setEditingMessageId,
      editingMessage,
      showTextEditor = true,
      showReplies = true,
    },
    ref,
  ) => {
    const {
      currentUser: { id: currentUserId },
    } = useCurrentUser();

    const renderItems = useCallback(
      (items: IThreadMessageItem[]) => (
        <ThreadMessageReadedProvider>
          <ThreadMessageForm
            ref={ref}
            editingMessage={editingMessage}
            editingMessageId={editingMessageId}
            isEditMode={isEditMode}
            parentId={messageId}
            setEditingMessageId={setEditingMessageId}
            setIsEditMode={setIsEditMode}
            showEditorBefore={showTextEditor}
            showReplies={showReplies}
            showTextEditor={showTextEditor}
          >
            <>
              {map(items, item => (
                <ThreadMessage
                  key={item.id}
                  className={style.reply}
                  editingMessageId={editingMessageId}
                  isEditMode={isEditMode}
                  message={item}
                  setEditingMessageId={setEditingMessageId}
                  setIsEditMode={setIsEditMode}
                />
              ))}
            </>
          </ThreadMessageForm>
        </ThreadMessageReadedProvider>
      ),
      [
        messageId,
        ref,
        isEditMode,
        setIsEditMode,
        editingMessageId,
        setEditingMessageId,
        editingMessage,
        showTextEditor,
        showReplies,
      ],
    );
    const _gqlConnectionVariables = useMemo(
      () => ({ ...gqlConnectionVariables, parentId: messageId }),
      [gqlConnectionVariables, messageId],
    );

    const {
      items,
      setItems,
      replaceItem,
      deleteItem,
      applyReaction,
    } = useThreadMessagesList();

    const handleNewDataLoaded = useCallback(
      (_items: IThreadMessageItem[]) => {
        setItems([..._items, ...items]);
      },
      [items, setItems],
    );

    const { anchorParentId, anchorId } = useAnchor();

    const anchor = (anchorParentId && anchorId) || undefined;

    const handleIncomingRealtimeMessage = useCallback(
      message => {
        if (
          ConversationMessageNotificationType.isCreated(
            message.notificationType,
          )
        ) {
          const { parentId, id } = message;

          if (
            !parentId ||
            parentId !== messageId ||
            items.some(x => x.id === id)
          ) {
            return;
          }

          return setItems([
            {
              ...message,
              isStarred: false,
              isLiked: false,
              likeCount: 0,
              threadSize: 0,
              unreadCount: 0,
            },
          ]);
        }

        if (
          ConversationMessageNotificationType.isDeleted(
            message.notificationType,
          )
        ) {
          return deleteItem(message.conversationItemId);
        }

        if (
          ConversationMessageNotificationType.isUpdated(
            message.notificationType,
          )
        ) {
          const { parentId, id, content, updatedAt } = message;
          const currentMessage = items.find(x => x.id === id);

          if (!parentId || parentId !== messageId || !currentMessage) {
            return;
          }

          return replaceItem({ ...currentMessage, content, updatedAt });
        }

        if (
          ConversationMessageNotificationType.isReacted(
            message.notificationType,
          )
        ) {
          return applyReaction(message.conversationItemId, {
            reactionType: message.reactionType,
            isCurrentUserReaction:
              message.participantPersonId === currentUserId,
          });
        }
      },
      [applyReaction, deleteItem, setItems, currentUserId, items, messageId],
    );

    useSocketHandler(
      `conversation/${conversationId}`,
      handleIncomingRealtimeMessage,
    );

    return (
      <FetchMoreScroll<IThreadMessageItem>
        anchor={anchor}
        className={style.replies}
        FetchMoreButton={FetchMoreButton}
        gqlConnectionQuery={gqlConnectionQuery}
        gqlConnectionVariables={_gqlConnectionVariables}
        hasEmptyPlaceholder={false}
        items={items}
        mode="DESC"
        renderItems={renderItems}
        onNewDataLoaded={handleNewDataLoaded}
      />
    );
  },
);

ThreadMessagesList.displayName = 'ThreadMessagesList';

export default ThreadMessagesList;
